.PHONY: help install dev test lint format type-check clean docs build publish

# Default target
help:
	@echo "Available commands:"
	@echo "  install     Install dependencies"
	@echo "  dev         Setup development environment"
	@echo "  test        Run tests"
	@echo "  test-cov    Run tests with coverage"
	@echo "  lint        Run linting"
	@echo "  format      Format code"
	@echo "  type-check  Run type checking"
	@echo "  clean       Clean build artifacts"
	@echo "  docs        Build documentation"
	@echo "  docs-serve  Serve documentation locally"
	@echo "  build       Build package"
	@echo "  publish     Publish package to PyPI"
	@echo "  pre-commit  Run pre-commit hooks"

# Installation
install:
	poetry install

dev: install
	poetry run pre-commit install
	@echo "Development environment setup complete!"

# Testing
test:
	poetry run pytest

test-cov:
	poetry run pytest --cov=src --cov-report=html --cov-report=term

# Code quality
lint:
	poetry run ruff check

format:
	poetry run ruff format

type-check:
	poetry run mypy src/

# Pre-commit
pre-commit:
	poetry run pre-commit run --all-files

# Documentation
docs:
	poetry run mkdocs build

docs-serve:
	poetry run mkdocs serve

# Build and publish
build:
	poetry build

publish:
	poetry publish

# Cleanup
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .ruff_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

# Development shortcuts
check: lint type-check test
	@echo "All checks passed!"

fix: format
	poetry run ruff check --fix

# Docker (optional)
docker-build:
	docker build -t sync .

docker-run:
	docker run -it --rm sync
