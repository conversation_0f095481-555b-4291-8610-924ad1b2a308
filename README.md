# Sync - Modern Python Project

A modern Python project template with best practices and development tools.

## Features

- 🚀 **Modern Python**: Python 3.11+ with type hints
- 📦 **Poetry**: Dependency management and packaging
- 🔧 **Ruff**: Ultra-fast Python linter and formatter
- 🧪 **Pytest**: Testing framework with coverage
- 🔍 **MyPy**: Static type checking
- 🪝 **Pre-commit**: Git hooks for code quality
- 📚 **MkDocs**: Documentation generation
- 🎯 **Typer**: Modern CLI framework
- 🌐 **HTTPX**: Modern HTTP client
- ✨ **Rich**: Beautiful terminal output

## Quick Start

### Prerequisites

- Python 3.11+
- Poetry (install from [python-poetry.org](https://python-poetry.org/docs/#installation))

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd sync
```

2. Install dependencies:
```bash
poetry install
```

3. Activate the virtual environment:
```bash
poetry shell
```

4. Install pre-commit hooks:
```bash
pre-commit install
```

## Development

### Running the application

```bash
# Using poetry
poetry run sync --help

# Or activate shell first
poetry shell
sync --help
```

### Testing

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=src --cov-report=html

# Run specific test file
poetry run pytest tests/test_example.py

# Run with markers
poetry run pytest -m "not slow"
```

### Code Quality

```bash
# Format code
poetry run ruff format

# Lint code
poetry run ruff check

# Fix linting issues
poetry run ruff check --fix

# Type checking
poetry run mypy src/

# Run all pre-commit hooks
pre-commit run --all-files
```

### Documentation

```bash
# Serve documentation locally
poetry run mkdocs serve

# Build documentation
poetry run mkdocs build
```

## Project Structure

```
sync/
├── src/sync/           # Source code
│   ├── __init__.py
│   ├── cli.py          # CLI interface
│   ├── core/           # Core business logic
│   └── utils/          # Utility functions
├── tests/              # Test files
├── docs/               # Documentation
├── pyproject.toml      # Project configuration
├── README.md           # This file
└── Makefile           # Development commands
```

## Available Commands

Use the Makefile for common development tasks:

```bash
make install        # Install dependencies
make test          # Run tests
make lint          # Run linting
make format        # Format code
make type-check    # Run type checking
make clean         # Clean build artifacts
make docs          # Build documentation
make dev           # Setup development environment
```

## Configuration

The project uses `pyproject.toml` for all configuration:

- **Poetry**: Dependencies and packaging
- **Ruff**: Linting and formatting rules
- **MyPy**: Type checking configuration
- **Pytest**: Test configuration
- **Coverage**: Coverage reporting

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Run tests and linting: `make test lint`
5. Commit your changes: `git commit -m "Add feature"`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
