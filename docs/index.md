# Sync Documentation

Welcome to the Sync project documentation!

## Overview

Sync is a modern Python project template that demonstrates best practices for Python development in 2024. It includes:

- Modern dependency management with Poetry
- Code quality tools (Ruff, MyPy, Pre-commit)
- Testing with Pytest
- CLI interface with Typer and Rich
- Documentation with MkDocs

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd sync

# Install dependencies
poetry install

# Activate virtual environment
poetry shell

# Install pre-commit hooks
pre-commit install
```

### Usage

The project includes a simple CLI application:

```bash
# Show help
sync --help

# Add two numbers
sync add 5 3

# Multiply two numbers
sync multiply 4 6

# Show application info
sync info

# Show version
sync version
```

## Development

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=src --cov-report=html

# Run only fast tests
poetry run pytest -m "not slow"
```

### Code Quality

```bash
# Format code
poetry run ruff format

# Lint code
poetry run ruff check --fix

# Type checking
poetry run mypy src/

# Run all pre-commit hooks
pre-commit run --all-files
```

## API Reference

### Calculator Class

The `Calculator` class provides basic arithmetic operations:

```python
from sync.core.example import Calculator

calc = Calculator()

# Basic operations
result = calc.add(2, 3)        # 5
result = calc.subtract(5, 2)   # 3
result = calc.multiply(3, 4)   # 12
result = calc.divide(10, 2)    # 5.0

# Advanced operations
result = calc.power(2, 3)      # 8
result = calc.factorial(5)     # 120
is_even = calc.is_even(4)      # True
```

## Project Structure

```
sync/
├── src/sync/           # Source code
│   ├── __init__.py
│   ├── cli.py          # CLI interface
│   ├── core/           # Core business logic
│   │   ├── __init__.py
│   │   └── example.py  # Calculator example
│   └── utils/          # Utility functions
├── tests/              # Test files
│   ├── __init__.py
│   └── test_example.py
├── docs/               # Documentation
├── pyproject.toml      # Project configuration
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

For more details, see the [README](../README.md).
